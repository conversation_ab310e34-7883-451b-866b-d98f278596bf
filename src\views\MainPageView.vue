<script setup>
import { ref, onMounted, onUnmounted } from 'vue'



</script>

<template>
<div id="main-page">
  <div></div>
	<div id="menu">
		<div class="splits"></div><div class="button">主界面</div>
		<div class="splits"></div><div class="button">安全<br>监测</div>
		<div class="splits"></div><div class="button">车列<br>电控</div>
		<div class="splits"></div><div class="button">视频<br>监控</div>
		<div class="splits"></div><div class="button">牵引<br>制动</div>
		<div class="splits"></div><div class="button">安全<br>环路</div>
		<div class="splits"></div><div class="button">健康<br>管理</div>
		<div class="splits"></div><div class="button"></div>
		<div class="splits"></div><div class="button">故障<br>记录</div>
		<div class="splits"></div><div class="button">系统<br>设置</div>
		<div class="splits"></div>
	</div>
</div>

</template>

<style scoped>
#main-page{
  padding: 0;
  margin: 0;
}


#bars{
	flex:1;
}
#body{
	flex: 1;
}
#menu{
  position: absolute;
  bottom: 0;
	display: flex;
	height: 100px;
}


.button{
	width: 80px;
	height: 80px;
	border-radius: 10px;
	border: 2px solid #666666;
	background: transparent;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18px;
	color: #ffffff
}
.splits{
	flex:1
}


</style>
