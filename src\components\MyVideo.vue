<script setup>
import { computed, onMounted, ref, useTemplateRef } from 'vue'
import videojs from 'video.js'
import 'video.js/dist/video-js.min.css'

// 定义组件属性并设置默认值
const props = defineProps({
  /** 视频地址 */
  src: {
    type: String,
    required: true
  },
  width: {
    type: String,
    default: '100%' // 直接在这里设置默认值
  },
  height: {
    type: String,
    default: '100%' // 直接在这里设置默认值
  }
})

// video标签引用
const videoRef = useTemplateRef('videoRef')
// video实例对象
let videoPlayer = null

// 计算视频容器样式
const videoWrapStyles = computed(() => {
  return {
    width: props.width || '100%',
    height: props.height || '100%'
  }
})

// 初始化videojs
const initVideo = () => {
  // https://gitcode.gitcode.host/docs-cn/video.js-docs-cn/docs/guides/options.html
  const options = {
    language: 'zh-CN', // 设置语言
    controls: true, // 是否显示控制条
    preload: 'auto', // 预加载
    autoplay: false, // 是否自动播放
    fluid: false, // 自适应宽高
    src: props.src // 要嵌入的视频源的源 URL
  }
  
  if (videoRef.value) {
    // 创建 video 实例
    videoPlayer = videojs(videoRef.value, options, onPlayerReady)
  }
}

// video初始化完成的回调函数
const onPlayerReady = () => {}

// 组件挂载后初始化视频
onMounted(() => {
  initVideo()
})
</script>

<template>
  <div :style="videoWrapStyles">
    <video id="my-player" ref="videoRef" class="video-js w-full h-full">
      <source :src="src"  type="video/mp4"/>
    </video>
  </div>
</template>

<style lang="css" scoped>
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
</style>
