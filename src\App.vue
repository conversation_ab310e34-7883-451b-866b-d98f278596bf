<script setup>
import { RouterLink, RouterView } from 'vue-router'
import HelloWorld from './components/HelloWorld.vue'
import TopBar from './components/TopBar.vue';
</script>

<template>
  <div class="black-box">
      <header>
        <TopBar/>
      </header>
    <RouterView />
  </div>


  
</template>

<style scoped>
.black-box {
  background-color: #2b2b2b;
  width: 1024px;
  height: 768px;
}
</style>

