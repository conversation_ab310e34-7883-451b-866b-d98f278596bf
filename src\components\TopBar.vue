<script setup>
import { ref, onMounted, onUnmounted } from 'vue'


const curtime = ref('00:00:00')
const curdate = ref('yyyy-mm-dd')
const curweek = ref('')
const weeks = ['星期日','星期一','星期二','星期三','星期四','星期五','星期六',]
const speed = ref(0)

const UpdateTime=()=>{
	const now = new Date();
	const yy = now.getFullYear();
	const mm = String(now.getMonth()+1).padStart(2,'0')
	const dd = String(now.getDate()).padStart(2,'0')
	const HH = String(now.getHours()).padStart(2,'0')
	const MM = String(now.getMinutes()).padStart(2,'0')
	const SS = String(now.getSeconds()).padStart(2,'0')
	curtime.value = HH+':'+MM+':'+SS
	curdate.value = yy+'-'+mm+'-'+dd
	const week = now.getDay()
	curweek.value = weeks[week]
}

onMounted(()=>{
	setInterval(UpdateTime,1000)
})
</script>

<template>
  <div id="head">
    <div class="headitem"><img src="/images/alarm.png"/></div>
    <div class="headitem" style="font-size:32px;color:#ffffff;text-align:right;">12</div>
    <div class="headitem" style="font-size:12px;color:#99ccff;text-align:left"><div style="height:5px;"></div><div>&nbsp;当前事件</div></div>
    <div class="headitem" style="font-size:32px;color:#ffffff;text-align:right;">{{speed}}</div>
    <div class="headitem" style="font-size:12px;color:#99ccff;text-align:left"><div style="height:5px;"></div><div>&nbsp;km/h</div></div>
    <div class="headitem" style="font-size:32px;color:#ffffff;text-align:right;">-5</div>
    <div class="headitem" style="font-size:12px;color:#99ccff;text-align:left"><div style="height:5px;"></div><div>&nbsp;℃</div></div>
    <div class="headitem" style="font-size:32px;color:#ffffff;text-align:right;">CR200J3-0001</div>
    <div class="headitem"></div>
    <div class="headitem" style="font-size:32px;color:#ffffff;text-align:right;">{{curtime}}</div>
    <div class="headitem" style="font-size:12px;color:#99ccff;text-align:left"><div style="height:5px;"></div><div>&nbsp;{{curdate}}&nbsp<br>&nbsp;{{curweek}}&nbsp</div></div>
  </div>
</template>

<style scoped>
#head{
	display: flex;
	width: 100%;
	height: 50px;
}

.headitem{
	flex: 1;
	background-color:#333333;
}
</style>